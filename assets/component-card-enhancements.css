/* Card Product Enhancements - Reviews and Submit Button */

/* Card Reviews Section */
.card__reviews {
  margin: 1rem 0;
  padding: 0.5rem 0;
}

.card__reviews .jdgm-widget {
  margin-bottom: 0.5rem;
}

.card__reviews .shopify-reviews-fallback {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.card__reviews .rating--small {
  font-size: 0.9em;
}

.card__reviews .rating-count {
  font-size: 1.2rem;
  color: rgba(var(--color-foreground), 0.7);
  margin: 0;
}

/* Card Submit Button */
.card__submit-button {
  margin-top: 1rem;
  padding-top: 1rem;
}

.card__submit-btn {
  width: 100%;
  padding: 1rem 1.5rem;
  font-size: 1.4rem;
  font-weight: 500;
  border-radius: var(--buttons-radius);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.card__submit-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(var(--color-shadow), 0.2);
}

.card__submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.card__submit-btn .loading-spinner {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
}

/* Responsive adjustments */
@media screen and (max-width: 749px) {
  .card__reviews {
    margin: 0.8rem 0;
  }
  
  .card__submit-btn {
    padding: 0.8rem 1.2rem;
    font-size: 1.3rem;
  }
}

/* Card layout improvements */
.card-wrapper .card__content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card-wrapper .card__information {
  flex-grow: 1;
}

.card-wrapper .card__submit-button {
  margin-top: auto;
}

/* Judge.me widget styling */
.jdgm-widget.jdgm-preview-badge {
  display: inline-block;
  margin: 0;
}

.jdgm-widget .jdgm-star {
  color: #ffc107;
}

.jdgm-widget .jdgm-rev-widg__summary-text {
  font-size: 1.2rem;
  color: rgba(var(--color-foreground), 0.7);
}

/* Hide Shopify reviews when Judge.me is active */
.jdgm-widget:not(:empty) + .shopify-reviews-fallback {
  display: none;
}

/* Animation for submit button */
@keyframes submitSuccess {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

.card__submit-btn.success {
  animation: submitSuccess 0.3s ease;
}

/* Loading state */
.card__submit-btn[aria-busy="true"] {
  pointer-events: none;
}

.card__submit-btn[aria-busy="true"] .loading-spinner {
  display: block;
}

.card__submit-btn:not([aria-busy="true"]) .loading-spinner {
  display: none;
}

/* Sold out styling */
.card__submit-btn:disabled {
  background-color: rgba(var(--color-foreground), 0.1);
  color: rgba(var(--color-foreground), 0.5);
  border-color: rgba(var(--color-foreground), 0.1);
}

/* Focus states for accessibility */
.card__submit-btn:focus-visible {
  outline: 2px solid rgb(var(--color-foreground));
  outline-offset: 2px;
}

.jdgm-widget:focus-visible {
  outline: 2px solid rgb(var(--color-foreground));
  outline-offset: 2px;
  border-radius: 4px;
}

/* Professional Review System Enhancements */
.card__professional-reviews {
  margin: 1.2rem 0;
  padding: 0.8rem 0;
}

.rating--professional {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.rating--professional .rating-star {
  font-size: 1.6rem;
  color: #ffc107;
  margin-right: 0.5rem;
}

.rating--professional + .rating-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.3rem;
  color: rgba(var(--color-foreground), 0.8);
  margin: 0;
}

.rating--professional + .rating-text .rating-count {
  font-size: 1.2rem;
  color: rgba(var(--color-foreground), 0.6);
  font-weight: 400;
}

/* Enhanced Price Section */
.card__price-section {
  margin: 1.5rem 0;
  padding: 1rem 0;
  border-top: 1px solid rgba(var(--color-foreground), 0.1);
}

.price--prominent {
  font-size: 2rem;
  font-weight: 700;
  color: rgb(var(--color-foreground));
}

.price--prominent .price__regular {
  font-size: 2rem;
}

.price--prominent .price__sale {
  font-size: 2rem;
  color: rgb(var(--color-base-accent-1));
}

.price--prominent .price__compare-at {
  font-size: 1.4rem;
  color: rgba(var(--color-foreground), 0.6);
  text-decoration: line-through;
}

/* Professional View Details Button */
.card__view-details {
  margin: 1.2rem 0;
}

.button--view-details {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.8rem;
  padding: 1rem 2rem;
  font-size: 1.4rem;
  font-weight: 500;
  text-decoration: none;
  border: 2px solid rgb(var(--color-base-accent-1));
  background-color: transparent;
  color: rgb(var(--color-base-accent-1));
  border-radius: var(--buttons-radius);
  transition: all 0.3s ease;
  width: 100%;
  text-align: center;
}

.button--view-details:hover {
  background-color: rgb(var(--color-base-accent-1));
  color: rgb(var(--color-base-background-1));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(var(--color-base-accent-1), 0.3);
}

.button--view-details .icon-arrow {
  width: 1.4rem;
  height: 1.4rem;
  transition: transform 0.3s ease;
}

.button--view-details:hover .icon-arrow {
  transform: translateX(4px);
}

/* Default rating stars styling - Enhanced */
.rating--default .rating-star {
  --color-rating-star: #ffc107;
  position: relative;
  font-family: Times, serif;
  font-size: 1.6rem;
  line-height: 1;
  letter-spacing: 0.1rem;
}

.rating--default .rating-star::before {
  content: "★★★★★";
  background: linear-gradient(
    90deg,
    var(--color-rating-star) var(--percent),
    rgba(0, 0, 0, 0.15) var(--percent)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Ensure proper star display for all ratings */
.card__professional-reviews .rating-star {
  --color-rating-star: #ffc107;
}

/* Responsive adjustments for new elements */
@media screen and (max-width: 749px) {
  .card__professional-reviews {
    margin: 1rem 0;
    padding: 0.6rem 0;
  }

  .rating--professional .rating-star {
    font-size: 1.4rem;
  }

  .rating--professional + .rating-text {
    font-size: 1.2rem;
  }

  .card__price-section {
    margin: 1.2rem 0;
    padding: 0.8rem 0;
  }

  .price--prominent {
    font-size: 1.8rem;
  }

  .button--view-details {
    padding: 0.8rem 1.5rem;
    font-size: 1.3rem;
  }
}
